"use client"

import * as React from "react"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CalendarDays, Clock, MapPin } from "lucide-react"
import { formatDate, formatTime, Task } from "@/app/api/parse-event/route"

interface TaskCalendarProps {
  tasks: Task[]
  onDateSelect?: (date: Date | undefined) => void
  selectedDate?: Date
}

export function TaskCalendar({ tasks, onDateSelect, selectedDate }: TaskCalendarProps) {
  const [date, setDate] = React.useState<Date | undefined>(selectedDate || new Date())

  // Group tasks by date for easy lookup
  const tasksByDate = React.useMemo(() => {
    const grouped: { [key: string]: Task[] } = {}
    tasks.forEach(task => {
      const dateKey = new Date(task.date).toDateString()
      if (!grouped[dateKey]) {
        grouped[dateKey] = []
      }
      grouped[dateKey].push(task)
    })
    return grouped
  }, [tasks])

  // Get tasks for selected date
  const selectedDateTasks = React.useMemo(() => {
    if (!date) return []
    const dateKey = date.toDateString()
    return tasksByDate[dateKey] || []
  }, [date, tasksByDate])

  const handleDateSelect = (newDate: Date | undefined) => {
    setDate(newDate)
    onDateSelect?.(newDate)
  }

  const getCategoryColor = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'meeting': return 'bg-blue-500'
      case 'exercise': case 'fitness': return 'bg-green-500'
      case 'personal': return 'bg-purple-500'
      case 'call': return 'bg-orange-500'
      case 'appointment': return 'bg-pink-500'
      default: return 'bg-gray-500'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Custom day content to show task indicators
  const renderDayContent = (day: Date) => {
    const dayTasks = tasksByDate[day.toDateString()] || []
    const dayNumber = day.getDate()
    
    return (
      <div className="relative w-full h-full flex flex-col items-center justify-center">
        <span className="text-sm">{dayNumber}</span>
        {dayTasks.length > 0 && (
          <div className="flex gap-0.5 mt-1 flex-wrap justify-center max-w-full">
            {dayTasks.slice(0, 3).map((task, index) => (
              <div
                key={index}
                className={`w-1.5 h-1.5 rounded-full ${getCategoryColor(task.category)} opacity-80`}
                title={`${task.title} at ${formatTime(task.startTime)}`}
              />
            ))}
            {dayTasks.length > 3 && (
              <div className="w-1.5 h-1.5 rounded-full bg-gray-400 opacity-80" 
                   title={`+${dayTasks.length - 3} more tasks`} />
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Calendar */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="h-5 w-5" />
              Task Calendar
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={handleDateSelect}
              className="w-full rounded-lg border-0"
              components={{
                DayContent: ({ date: dayDate }) => renderDayContent(dayDate)
              }}
            />
          </CardContent>
        </Card>
      </div>

      {/* Task Details for Selected Date */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {date ? formatDate(date.toISOString()) : 'Select a Date'}
            </CardTitle>
            {selectedDateTasks.length > 0 && (
              <Badge variant="secondary">
                {selectedDateTasks.length} task{selectedDateTasks.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </CardHeader>
          <CardContent>
            {selectedDateTasks.length === 0 ? (
              <p className="text-gray-500 text-sm">No tasks for this date</p>
            ) : (
              <div className="space-y-3">
                {selectedDateTasks
                  .sort((a, b) => a.startTime.localeCompare(b.startTime))
                  .map((task, index) => (
                    <div key={index} className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <h4 className="font-medium text-sm leading-tight">{task.title}</h4>
                        {task.priority && (
                          <Badge variant="outline" className={`text-xs ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </Badge>
                        )}
                      </div>
                      
                      {task.category && (
                        <Badge variant="outline" className="mb-2 text-xs">
                          {task.category}
                        </Badge>
                      )}

                      <div className="space-y-1 text-xs text-gray-600">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            {formatTime(task.startTime)}
                            {task.endTime && ` - ${formatTime(task.endTime)}`}
                            {task.duration && !task.endTime && ` (${task.duration})`}
                          </span>
                        </div>

                        {task.location && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>{task.location}</span>
                          </div>
                        )}

                        {task.description && (
                          <p className="text-gray-500 mt-2">{task.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Legend */}
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="text-sm">Category Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <span>Meeting</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span>Exercise</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                <span>Personal</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                <span>Call</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-pink-500"></div>
                <span>Appointment</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                <span>Other</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}