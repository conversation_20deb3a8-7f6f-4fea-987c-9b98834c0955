'use client';

import { useState } from 'react';
import { parseTasks, Task, formatDate, formatTime } from './api/parse-event/route';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { CalendarDays, Clock, MapPin, FileText, AlertCircle } from 'lucide-react';
import { Calendar18 } from "@/components/ui/calendar_18"

import { Authenticated, Unauthenticated } from 'convex/react'
import { SignInButton, UserButton } from '@clerk/nextjs'
import { useQuery } from 'convex/react'
import { api } from '../convex/_generated/api'

import { TaskCalendar } from "@/components/ui/taskCalendar"

/*
export default function Home() {
  return (
    <>
      <Authenticated>
        <UserButton />
        <Content />
      </Authenticated>
      <Unauthenticated>
        <SignInButton />
      </Unauthenticated>
    </>
  )
}*/

function Content() {
  const messages = useQuery(api.messages.getForCurrentUser)
  return <div>Authenticated content: {messages?.length}</div>
}

export default function TaskPage() {

  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');

  const handleParsePrompt = async () => {
    if (!prompt.trim()) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const parsedTasks = await parseTasks(prompt);
      console.log("Parsed Tasks:", parsedTasks);
      setTasks(parsedTasks);
    } catch (err) {
      console.error("Error parsing tasks:", err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleParsePrompt();
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500 hover:bg-red-600';
      case 'medium': return 'bg-yellow-500 hover:bg-yellow-600';
      case 'low': return 'bg-green-500 hover:bg-green-600';
      default: return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'meeting': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'exercise': case 'fitness': return 'bg-green-100 text-green-800 border-green-200';
      case 'personal': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'call': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'appointment': return 'bg-pink-100 text-pink-800 border-pink-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (

    
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-2">Task Parser</h1>
        <p className="text-gray-600">Parse multiple tasks from natural language</p>
      </div>


      {/*console log */}
      
      {/* Input Section */}
      <>
      <Authenticated>
        <UserButton />
        <Content />
      </Authenticated>
      <Unauthenticated>
        <SignInButton />
      </Unauthenticated>
    </>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Enter Multiple Tasks
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="task-input" className="text-sm font-medium">
              Describe your tasks in natural language
            </Label>
            <p className="text-sm text-gray-500 mt-1">
              Example: "Monday a meeting at 3pm, yoga classes at 5pm at the gym, call mom by 4pm next Monday"
            </p>
          </div>
          
          <Textarea
            id="task-input"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter your tasks here... You can include multiple tasks, times, dates, and locations in any order."
            className="min-h-[120px] resize-none"
          />
          
          <Button
            onClick={handleParsePrompt}
            disabled={!prompt.trim() || loading}
            className="w-full sm:w-auto"
            size="lg"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Parsing Tasks...
              </>
            ) : (
              'Parse Tasks'
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span className="font-semibold">Error:</span>
              {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {tasks.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">Parsed Tasks</h2>
            <Badge variant="secondary" className="text-sm">
              {tasks.length} task{tasks.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {tasks.map((task, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between gap-2">
                    <CardTitle className="text-lg leading-tight">{task.title}</CardTitle>
                    {task.priority && (
                      <Badge className={`${getPriorityColor(task.priority)} text-white text-xs`}>
                        {task.priority}
                      </Badge>
                    )}
                  </div>
                  {task.category && (
                    <Badge variant="outline" className={`w-fit text-xs ${getCategoryColor(task.category)}`}>
                      {task.category}
                    </Badge>
                  )}
                </CardHeader>

                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <CalendarDays className="h-4 w-4 text-gray-500" />
                    <span>{formatDate(task.date)}</span>

                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span>
                      {formatTime(task.startTime)}
                      {task.endTime && ` - ${formatTime(task.endTime)}`}
                      {task.duration && !task.endTime && ` (${task.duration})`}
                    </span>
                  </div>

                  {task.location && (
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{task.location}</span>
                    </div>
                  )}

                  {task.description && (
                    <div className="pt-2 border-t">
                      <p className="text-sm text-gray-600">{task.description}</p>
                    </div>
                  )}

                  {task.notes && (
                    <div className="pt-2 border-t">
                      <p className="text-xs text-gray-500 italic">{task.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
      {/*Calendar18 */}
      {/* Calendar View */}
      {tasks.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">Calendar View</h2>
          </div>
          <TaskCalendar tasks={tasks} />
        </div>
      )}

      {/* Example prompts */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-lg">Example Prompts to Try</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="p-3 bg-white rounded border cursor-pointer hover:bg-gray-50 transition-colors"
                 onClick={() => setPrompt("Monday a meeting at 3pm, yoga classes at 5pm at the gym, call mom by 4pm next Monday")}>
              <code className="text-blue-600">"Monday a meeting at 3pm, yoga classes at 5pm at the gym, call mom by 4pm next Monday"</code>
            </div>
            <div className="p-3 bg-white rounded border cursor-pointer hover:bg-gray-50 transition-colors"
                 onClick={() => setPrompt("Doctor appointment tomorrow at 2pm, pick up groceries at 6pm, dinner with Sarah on Friday")}>
              <code className="text-blue-600">"Doctor appointment tomorrow at 2pm, pick up groceries at 6pm, dinner with Sarah on Friday"</code>
            </div>
            <div className="p-3 bg-white rounded border cursor-pointer hover:bg-gray-50 transition-colors"
                 onClick={() => setPrompt("Team standup every Monday at 9am for 30 minutes, lunch with client at 12:30pm downtown")}>
              <code className="text-blue-600">"Team standup every Monday at 9am for 30 minutes, lunch with client at 12:30pm downtown"</code>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-3">Click on any example to try it out!</p>
        </CardContent>
      </Card>
    </div>
  );
}